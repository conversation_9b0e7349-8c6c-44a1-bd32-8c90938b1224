<!doctype html>
<html lang="en-US"
    class="no-featured-area is-header-small is-body-full-width is-single-post-title-default is-post-title-align-center is-post-media-fixed is-blog-text-align-left is-meta-with-icons is-header-light is-header-full-width is-header-parallax-no is-menu-sticky is-menu-fixed-width is-menu-align-center is-menu-light is-submenu-light is-submenu-align-left is-menu-uppercase is-featured-area-full is-slider-buttons-center-margin is-slider-buttons-rounded is-slider-buttons-dark is-slider-title-label is-slider-parallax is-slider-title-none-uppercase is-slider-more-link-show-on-hover is-slider-more-link-border-bottom is-slider-text-align-center is-slider-v-align-center is-slider-h-align-center is-link-box-title-default is-link-box-title-transform-none is-link-box-text-align-center is-link-box-v-align-center is-link-box-parallax is-intro-align-center is-intro-text-dark is-intro-parallax-no is-more-link-border-bottom-light is-about-author-minimal is-related-posts-parallax is-related-posts-fixed is-share-links-boxed is-tagcloud-minimal is-nav-single-rounded is-nav-single-no-animated is-comments-minimal is-comments-image-rounded is-comment-form-boxed is-comment-form-border is-sidebar-right is-sidebar-sticky is-sidebar-align-left is-widget-title-align-left is-widget-bottomline is-trending-posts-default is-footer-subscribe-light is-footer-widgets-align-left is-footer-full-width is-meta-uppercase is-slider-dots-rounded-line-grow is-site-title-uppercase is-top-bar-mobile-left-visible is-top-bar-uppercase is-top-bar-full is-sub-menu-ani-fade-in-left is-menu-hover-badge is-menu-hover-badge-round is-copyright-uppercase is-logo-bg-stretch-left is-header-sticky-shadow-soft-shorter is-header-transparent-border-bottom is-header-bg-blur-slightly is-footer-border-top is-footer-border-light"
    data-title-ratio="0.7" data-link-box-title-ratio="0.5" data-generic-button-style="" data-header-bg-shape="">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="http://gmpg.org/xfn/11">
    <title>About - Motiff Square</title>
    <meta name='robots' content='max-image-preview:large' />
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
    </style>
    <link rel='dns-prefetch' href='https://fonts.googleapis.com' />
    <link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Feed"
        href="https://themes.pixelwars.org/interique/demo-01/feed/" />
    <link rel="alternate" type="application/rss+xml" title="Motiff Square &raquo; Comments Feed"
        href="https://themes.pixelwars.org/interique/demo-01/comments/feed/" />
    <script type="text/javascript">
        /* <![CDATA[ */
        window._wpemojiSettings = { "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/", "ext": ".png", "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/", "svgExt": ".svg", "source": { "concatemoji": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1" } };
        /*! This file is auto-generated */
        !function (i, n) { var o, s, e; function c(e) { try { var t = { supportTests: e, timestamp: (new Date).valueOf() }; sessionStorage.setItem(o, JSON.stringify(t)) } catch (e) { } } function p(e, t, n) { e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0); var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data), r = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data)); return t.every(function (e, t) { return e === r[t] }) } function u(e, t, n) { switch (t) { case "flag": return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\uddfa\ud83c\uddf3", "\ud83c\uddfa\u200b\ud83c\uddf3") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f"); case "emoji": return !n(e, "\ud83d\udc26\u200d\ud83d\udd25", "\ud83d\udc26\u200b\ud83d\udd25") }return !1 } function f(e, t, n) { var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : i.createElement("canvas"), a = r.getContext("2d", { willReadFrequently: !0 }), o = (a.textBaseline = "top", a.font = "600 32px Arial", {}); return e.forEach(function (e) { o[e] = t(a, e, n) }), o } function t(e) { var t = i.createElement("script"); t.src = e, t.defer = !0, i.head.appendChild(t) } "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", s = ["flag", "emoji"], n.supports = { everything: !0, everythingExceptFlag: !0 }, e = new Promise(function (e) { i.addEventListener("DOMContentLoaded", e, { once: !0 }) }), new Promise(function (t) { var n = function () { try { var e = JSON.parse(sessionStorage.getItem(o)); if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests } catch (e) { } return null }(); if (!n) { if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try { var e = "postMessage(" + f.toString() + "(" + [JSON.stringify(s), u.toString(), p.toString()].join(",") + "));", r = new Blob([e], { type: "text/javascript" }), a = new Worker(URL.createObjectURL(r), { name: "wpTestEmojiSupports" }); return void (a.onmessage = function (e) { c(n = e.data), a.terminate(), t(n) }) } catch (e) { } c(n = f(s, u, p)) } t(n) }).then(function (e) { for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]); n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function () { n.DOMReady = !0 } }).then(function () { return e }).then(function () { var e; n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji))) })) }((window, document), window._wpemojiSettings);
        /* ]]> */
    </script>
    <style id='wp-emoji-styles-inline-css' type='text/css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <style id='classic-theme-styles-inline-css' type='text/css'>
        /*! This file is auto-generated */
        .wp-block-button__link {
            color: #fff;
            background-color: #32373c;
            border-radius: 9999px;
            box-shadow: none;
            text-decoration: none;
            padding: calc(.667em + 2px) calc(1.333em + 2px);
            font-size: 1.125em
        }

        .wp-block-file__button {
            background: #32373c;
            color: #fff;
            text-decoration: none
        }
    </style>
    <style id='global-styles-inline-css' type='text/css'>
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 13px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 36px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :where(.is-layout-flex) {
            gap: 0.5em;
        }

        :where(.is-layout-grid) {
            gap: 0.5em;
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }
    </style>
    <link rel='stylesheet' id='fontello-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/css/fonts/fontello/css/fontello.css'
        type='text/css' media='all' />
    <link rel='stylesheet' id='pixelwars-core-shortcodes-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/css/shortcodes.css'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-grid-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/grid.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-helper-parts-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/helper-parts.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='qi-addons-for-elementor-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/css/main.min.css?ver=1.8.9'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-font-texgyreadventor-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/fonts/texgyreadventor/stylesheet.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-font-now-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/fonts/now/stylesheet.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-fonts-css'
        href='//fonts.googleapis.com/css?family=Jost%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i%7CBricolage+Grotesque%3A100%2C100i%2C200%2C200i%2C300%2C300i%2C400%2C400i%2C500%2C500i%2C600%2C600i%2C700%2C700i%2C800%2C800i%2C900%2C900i&#038;ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='normalize-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/normalize.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='bootstrap-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/bootstrap.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='fluidbox-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/fluidbox/fluidbox.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='magnific-popup-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/magnific-popup.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='owl-carousel-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/owl-carousel/owl.carousel.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-main-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/main.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-768-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/768.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-992-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/css/992.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='interique-style-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/style.css?ver=6.8.1'
        type='text/css' media='all' />

    <style id='interique-style-inline-css' type='text/css'>
        @font-face {
            font-family: 'TeXGyreAdventor';
            src: url('./TeXGyreAdventor-Italic.woff2') format('woff2'),
                url('./TeXGyreAdventor-Italic.woff') format('woff');
            font-weight: normal;
            font-style: italic;
        }

        @font-face {
            font-family: 'TeXGyreAdventor';
            src: url('./TeXGyreAdventor-Bold.woff2') format('woff2'),
                url('./TeXGyreAdventor-Bold.woff') format('woff');
            font-weight: bold;
            font-style: normal;
        }

        @font-face {
            font-family: 'TeXGyreAdventor';
            src: url('./TeXGyreAdventor-Regular.woff2') format('woff2'),
                url('./TeXGyreAdventor-Regular.woff') format('woff');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'TeXGyreAdventor';
            src: url('./TeXGyreAdventor-BoldItalic.woff2') format('woff2'),
                url('./TeXGyreAdventor-BoldItalic.woff') format('woff');
            font-weight: bold;
            font-style: italic;
        }



        .site-title {
            font-family: 'TeXGyreAdventor', sans-serif;
        }

        .nav-menu,
        .entry-meta,
        .owl-nav,
        label,
        .page-links,
        .navigation,
        .entry-title i,
        .site-info,
        .filters {
            font-family: 'Jost';
        }

        .widget-title {
            font-family: 'TeXGyreAdventor', sans-serif;
        }

        h1,
        .entry-title,
        .footer-subscribe h3,
        .widget_categories ul li,
        .widget_recent_entries ul li a,
        .widget_pages ul li,
        .widget_nav_menu ul li,
        .widget_archive ul li,
        .widget_most_recommended_posts ul li a,
        .widget_calendar table caption,
        .tptn_title,
        .nav-single a,
        .widget_recent_comments ul li,
        .widget_product_categories ul li,
        .widget_meta ul li,
        .widget_rss ul a.rsswidget {
            font-family: 'Bricolage Grotesque';
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .tab-titles {
            font-family: 'Jost';
        }

        .slider-box .entry-title {
            font-family: 'Now', sans-serif;
        }

        body {
            font-family: 'Jost';
        }

        .link-box .entry-title {
            font-family: 'Now', sans-serif;
        }

        .button,
        button,
        html .elementor-button,
        html .ekit-wid-con .elementskit-btn,
        html .ekit-wid-con .ekit_creative_button,
        .more-link {
            font-family: 'Jost';
        }

        .top-bar {
            font-family: 'Jost';
        }

        @media screen and (min-width: 992px) {
            .site-header .site-title {
                font-size: 24px;
            }
        }

        @media screen and (min-width: 992px) {
            .is-header-smaller .site-header.clone .site-title {
                font-size: 24px;
            }
        }

        @media screen and (max-width: 991px) {
            .site-header .site-title {
                font-size: 14px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-small .entry-title {
                font-size: 24px;
            }
        }

        @media screen and (min-width: 992px) {
            h1 {
                font-size: 102px;
            }
        }

        @media screen and (min-width: 992px) {
            html {
                font-size: 16px;
            }
        }

        @media screen and (max-width: 991px) {
            html {
                font-size: 15px;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                font-size: 13px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-stream .entry-content {
                font-size: 16px;
            }
        }

        @media screen and (min-width: 992px) {
            .blog-stream.blog-small .entry-content {
                font-size: 15px;
            }
        }

        .widget-title {
            font-size: 13px;
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                font-size: 12px;
            }
        }

        .top-bar {
            font-size: 12px;
        }

        .site-footer .site-info {
            font-size: 12px;
        }

        .site-title {
            font-weight: 400;
        }

        h1,
        .entry-title,
        .footer-subscribe h3 {
            font-weight: 500;
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .comment-meta .fn {
            font-weight: 500;
        }

        .slider-box .entry-title {
            font-weight: 700;
        }

        .widget-title {
            font-weight: 700;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                font-weight: 500;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                font-weight: 400;
            }
        }

        .link-box .entry-title {
            font-weight: 700;
        }

        .site-description {
            font-weight: 400;
        }

        .top-bar {
            font-weight: 500;
        }

        .site-footer .site-info {
            font-weight: 500;
        }

        .entry-meta {
            font-weight: 500;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul {
                letter-spacing: 0px;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul {
                letter-spacing: 0px;
            }
        }

        .widget-title {
            letter-spacing: 3px;
        }

        .site-footer .site-info {
            letter-spacing: 3px;
        }

        h1,
        .entry-title,
        .footer-subscribe h3,
        .widget_categories ul li,
        .widget_recent_entries ul li,
        .widget_pages ul li,
        .widget_archive ul li,
        .widget_calendar table caption,
        .tptn_title,
        .nav-single a {
            text-transform: none;
        }

        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .comment-meta .fn {
            text-transform: none;
        }

        @media screen and (min-width: 992px) {
            html {
                line-height: 1.6;
            }
        }

        @media screen and (min-width: 992px) {
            .header-bg-shape {
                height: 50px;
            }
        }

        .header-wrap:after {
            bottom: -1px;
        }

        @media screen and (min-width: 992px) {
            .site-title img {
                max-height: 44px;
            }
        }

        @media screen and (max-width: 991px) {
            .site-title img {
                max-height: 24px;
            }
        }

        .top-bar {
            line-height: 36px;
        }

        html .site-header .site-title a {
            padding: 12px 24px;
        }

        @media screen and (min-width: 992px) {
            .site {
                margin-top: 0px;
                margin-bottom: 0px;
            }
        }

        .layout-medium,
        .is-header-row .header-wrap-inner,
        .is-header-small .header-wrap-inner,
        .is-menu-bar.is-menu-fixed-bg .menu-wrap,
        .is-header-fixed-width .header-wrap,
        .is-header-fixed-width.is-menu-bar .site-navigation,
        .is-header-float-box:not(.is-header-float-box-menu) .site-header:not(.clone) .header-wrap,
        .is-header-float-box.is-menu-bar .site-header:not(.clone) .site-navigation:not(.clone),
        .is-body-boxed .site,
        .is-body-boxed .header-wrap,
        .is-body-boxed.is-menu-bar .site-navigation,
        .is-body-boxed:not(.is-menu-bar) .site-header,
        .is-middle-boxed .site-main,
        .intro-content,
        .is-footer-boxed .site-footer,
        .is-content-boxed .site-main .layout-fixed,
        .top-bar .top-bar-wrap,
        .is-top-bar-fixed .top-bar,
        .is-top-bar-fixed-bg .top-bar,
        .is-menu-bottom.is-menu-bottom-overflow .site-header:not(.clone) .site-navigation:not(.clone) .menu-wrap,
        .site-branding-wrap,
        .is-header-border-fixed .header-wrap:after,
        .is-header-border-fixed .menu-wrap:after,
        html .tutor-container,
        html .lp-content-area,
        html .learn-press-breadcrumb {
            max-width: 1140px;
            margin-left: auto;
            margin-right: auto;
        }

        .layout-fixed,
        .blog-list,
        .blog-regular,
        .is-content-boxed .single .site-content,
        .is-content-boxed .page .site-content {
            max-width: 800px;
        }

        @media screen and (min-width: 992px) {

            .is-header-small .header-wrap,
            .is-menu-bar .nav-menu>ul>li,
            .is-header-vertical .nav-menu>ul>li {
                line-height: 80px;
            }

            .is-header-small .site-branding {
                max-height: 80px;
            }

        }

        @media screen and (min-width: 992px) {

            .is-header-small.is-header-smaller .site-header.clone .header-wrap,
            .is-header-row.is-header-smaller .site-header.clone .nav-menu>ul>li,
            .is-menu-bar.is-header-smaller .site-navigation.clone .nav-menu>ul>li,
            .is-menu-bar.is-header-smaller .site-header.clone .site-navigation .nav-menu>ul>li {
                line-height: 70px;
            }

            .is-header-small.is-header-smaller .site-header.clone .site-branding {
                max-height: 70px;
            }

            /* Fix for logo positioning and centering */
            .is-header-small .site-branding {
                position: absolute !important;
                z-index: 900 !important;
                top: 50% !important;
                left: 32px !important;
                transform: translateY(-50%) !important;
                width: auto !important;
                max-width: 316px !important;
                padding: 0 !important;
                text-align: left !important;
                display: flex !important;
                align-items: center !important;
            }

            .is-header-small .site-title {
                margin: 0 !important;
                line-height: 1 !important;
            }

            .is-header-small .site-title img {
                margin-top: 0 !important;
                vertical-align: middle !important;
            }

            /* Social Media Icons Styling */
            .social-media-icons {
                display: flex;
                gap: 15px;
                align-items: center;
                justify-content: flex-end;
                padding-right: 32px;
            }

            .social-icon {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 35px;
                height: 35px;
                border-radius: 50%;
                background-color: rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
                text-decoration: none;
            }

            .social-icon:hover {
                background-color: rgba(255, 255, 255, 0.2);
                transform: translateY(-2px);
            }

            .social-icon svg {
                width: 18px;
                height: 18px;
                fill: white;
            }

            /* Responsive adjustments for social icons */
            @media screen and (max-width: 991px) {
                .social-media-icons {
                    display: none;
                }
            }

        }

        .button.is-primary,
        .button.is-primary:after,
        html .elementor-button,
        .elementor-button.elementor-size-xs,
        .elementor-button.elementor-size-sm,
        .elementor-button.elementor-size-md,
        .elementor-button.elementor-size-lg,
        .elementor-button.elementor-size-xl,
        html .ekit-wid-con .elementskit-btn,
        html .ekit-wid-con .ekit_creative_button {
            border-radius: 0px;
        }

        .button.is-secondary,
        .button.is-secondary:after,
        .elementor-element.elementor-button-info .elementor-button {
            border-radius: 30px;
        }

        a {
            color: #d84156;
        }

        a:hover {
            color: #c60035;
        }

        .site-header .header-wrap {
            background-color: #fffefc;
        }

        html:not(.is-menu-bottom) .site-header .header-bg-shape {
            color: #fffefc;
        }

        .header-wrap:before {
            background: #fffefc;
        }

        .header-wrap:before {
            opacity: 0;
        }

        .is-header-half-transparent:not(.is-menu-toggled-on) .site-header:not(.clone) .header-wrap:before {
            opacity: 0.45;
        }

        .header-wrap:after {
            opacity: 0.08;
        }

        .site-header .menu-wrap {
            background-color: #ffffff;
        }

        html.is-menu-bottom .site-header .header-bg-shape {
            color: #ffffff;
        }

        @media screen and (min-width: 992px) {
            .nav-menu>ul>li.current-menu-item>a {
                color: #0a0a0a !important;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu li.current-menu-item>a .link-text:before {
                background-color: #bbcfbd !important;
                border-color: #bbcfbd !important;
            }
        }

        @media screen and (min-width: 992px) {

            html .nav-menu>ul>li>a:hover,
            .nav-menu>ul>li.has-submenu:hover>a {
                color: #1c1a17;
            }
        }

        @media screen and (min-width: 992px) {

            html.loaded .nav-menu ul li a .link-text:before,
            .nav-menu li.has-submenu:hover>a .link-text:before {
                background-color: #d6cfc2;
                border-color: #d6cfc2;
            }
        }

        @media screen and (min-width: 992px) {
            .nav-menu ul ul li.current-menu-item>a .link-text {
                color: #0a0606 !important;
            }
        }

        @media screen and (min-width: 992px) {

            html .nav-menu ul ul li a:hover .link-text,
            .nav-menu ul ul li.has-submenu:hover>a .link-text {
                color: #111111;
            }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        blockquote,
        .tab-titles {
            color: #020014;
        }

        body {
            color: #32455e;
        }

        body {
            background: #fffefc;
        }

        .site .footer-subscribe {
            background: #ebe6da;
        }

        .site-footer .site-info {
            background-color: #ffffff;
        }

        .site-footer .site-info {
            color: #0a0a0a;
        }

        .button.is-primary {
            color: #222222;
        }

        .button.is-primary.is-shadow,
        .button.is-primary.is-solid,
        .button.is-primary.is-solid-light,
        html .elementor-button,
        html .ekit-wid-con .elementskit-btn {
            background-color: #222222;
        }

        .button.is-primary.is-shadow {
            box-shadow: 0px 18px 23px -6px #222222;
        }

        .button.is-primary:hover {
            color: #215aed;
        }

        .button.is-primary.is-shadow:hover,
        .button.is-primary.is-solid:hover,
        .button.is-primary.is-solid-light:hover,
        .button.is-primary.is-shift:after,
        .button.is-primary.is-circle:before,
        html .elementor-button:hover,
        html .ekit-wid-con .elementskit-btn:hover {
            background-color: #215aed;
        }

        .button.is-primary.is-shadow:hover {
            box-shadow: 0px 2px 10px -5px #215aed;
        }

        .button.is-secondary {
            color: #f22000;
        }

        .button.is-secondary.is-shadow,
        .button.is-secondary.is-solid,
        .button.is-secondary.is-solid-light,
        .elementor-element.elementor-button-info .elementor-button {
            background-color: #f22000;
        }

        .button.is-secondary.is-shadow {
            box-shadow: 0px 18px 23px -6px #f22000;
        }

        .button.is-secondary:hover {
            color: #0026ff;
        }

        .button.is-secondary.is-shadow:hover,
        .button.is-secondary.is-solid:hover,
        .button.is-secondary.is-solid-light:hover,
        .button.is-secondary.is-shift:after,
        .button.is-secondary.is-circle:before,
        .elementor-element.elementor-button-info .elementor-button:hover {
            background-color: #0026ff;
        }

        .button.is-secondary.is-shadow:hover {
            box-shadow: 0px 2px 10px -5px #0026ff;
        }

        .top-bar,
        .top-bar select option {
            background-color: #262251;
        }

        .top-bar {
            background: linear-gradient(90deg, #262251 30%, #d10600 100%);
        }

        .header-wrap:after {
            color: #222222;
        }

        .is-menu-bar .menu-wrap:after {
            color: #222222;
        }
    </style>
    <link rel='stylesheet' id='swiper-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/plugins/swiper/8.4.5/swiper.min.css?ver=8.4.5'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-frontend-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/frontend.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-post-6-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/css/post-6.css?ver=1748102204'
        type='text/css' media='all' />
    <link rel='stylesheet' id='e-animation-fadeIn-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/lib/animations/styles/fadeIn.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='e-swiper-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/conditionals/e-swiper.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='widget-text-editor-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/widget-text-editor.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='widget-heading-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='widget-spacer-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/css/widget-spacer.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='e-animation-fadeInUp-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/lib/animations/styles/fadeInUp.min.css?ver=3.29.0'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-post-17782-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/css/post-17782.css?ver=1748200161'
        type='text/css' media='all' />
    <link rel='stylesheet' id='bdt-uikit-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/bdt-uikit.css?ver=3.21.7'
        type='text/css' media='all' />
    <link rel='stylesheet' id='prime-slider-site-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/css/prime-slider-site.css?ver=3.17.12'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-roboto-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/google-fonts/css/roboto.css?ver=1745821354'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-robotoslab-css'
        href='https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/elementor/google-fonts/css/robotoslab.css?ver=1745821356'
        type='text/css' media='all' />
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/jquery.min.js?ver=3.7.1"
        id="jquery-core-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1"
        id="jquery-migrate-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/js/bdt-uikit.min.js?ver=3.21.7"
        id="bdt-uikit-js"></script>
    <link rel="https://api.w.org/" href="https://themes.pixelwars.org/interique/demo-01/wp-json/" />
    <link rel="alternate" title="JSON" type="application/json"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/wp/v2/pages/17782" />
    <link rel="EditURI" type="application/rsd+xml" title="RSD"
        href="https://themes.pixelwars.org/interique/demo-01/xmlrpc.php?rsd" />
    <meta name="generator" content="WordPress 6.8.1" />
    <link rel="canonical" href="about.html" />
    <link rel='shortlink' href='https://themes.pixelwars.org/interique/demo-01/?p=17782' />
    <link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fabout-us%2F" />
    <link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"
        href="https://themes.pixelwars.org/interique/demo-01/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fthemes.pixelwars.org%2Finterique%2Fdemo-01%2Fabout-us%2F&#038;format=xml" />
    <meta name="generator"
        content="Elementor 3.29.0; features: e_font_icon_svg, additional_custom_breakpoints, e_local_google_fonts, e_element_cache; settings: css_print_method-external, google_font-enabled, font_display-swap">
    <style>
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
            background-image: none !important;
        }

        @media screen and (max-height: 1024px) {

            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }

        @media screen and (max-height: 640px) {

            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }
    </style>
</head>

<body
    class="wp-singular page-template page-template-elementor_header_footer page page-id-17782 wp-theme-interique qodef-qi--no-touch qi-addons-for-elementor-1.8.9 elementor-default elementor-template-full-width elementor-kit-6 elementor-page elementor-page-17782">
    <div id="page" class="hfeed site">
        <header id="masthead" class="site-header" role="banner">
            <div class="header-wrap" data-parallax-video="">
                <div class="header-wrap-inner">
                    <div class="site-branding">
                        <div class="site-branding-wrap">
                            <div class="site-branding-left">
                            </div> <!-- .site-branding-left -->

                            <div class="site-branding-center">
                                <h1 class="site-title">
                                    <a href="index.html" rel="home">
                                        <img src="./assets/logo.png" alt="Motiff Square">
                                    </a>
                                </h1> <!-- .site-title -->
                                <p class="site-description">
                                    Freelance Interior Designer – Personal, creative, and tailored design for homes and
                                    boutique spaces.
                                </p> <!-- .site-description -->
                            </div> <!-- .site-branding-center -->
                            <div class="site-branding-right">
                                <div class="social-media-icons">
                                    <a href="#" class="social-icon facebook" target="_blank" aria-label="Facebook">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                        </svg>
                                    </a>
                                    <a href="#" class="social-icon instagram" target="_blank" aria-label="Instagram">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                        </svg>
                                    </a>
                                    <a href="#" class="social-icon youtube" target="_blank" aria-label="YouTube">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                        </svg>
                                    </a>
                                    <a href="#" class="social-icon linkedin" target="_blank" aria-label="LinkedIn">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                    </a>
                                </div>
                            </div> <!-- .site-branding-right -->
                        </div> <!-- .site-branding-wrap -->
                    </div> <!-- .site-branding -->

                    <nav id="site-navigation" class="main-navigation site-navigation" role="navigation">
                        <div class="menu-wrap">
                            <div class="layout-medium">
                                <a class="menu-toggle">
                                    <span class="lines"></span>
                                </a> <!-- .menu-toggle -->
                                <div class="nav-menu">
                                    <ul id="menu-mymenu" class="">
                                        <li id="menu-item-15712"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-has-children menu-item-15712">
                                            <a href="index.html">Home</a>
                                            <!-- <ul class="sub-menu">
                                                <li id="menu-item-18584"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-18584">
                                                    <a href="index.html">Home
                                                        01</a></li>
                                                <li id="menu-item-18345"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18345">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-02/">Home
                                                        02</a></li>
                                                <li id="menu-item-18342"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18342">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-03/">Home
                                                        03</a></li>
                                                <li id="menu-item-18343"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18343">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-04/">Home
                                                        04</a></li>
                                                <li id="menu-item-18344"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18344">
                                                    <a href="https://themes.pixelwars.org/interique/demo-01/home-05/">Home
                                                        05</a></li>
                                            </ul> -->
                                        </li>
                                        <li id="menu-item-18246"
                                            class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-17782 current_page_item menu-item-18246">
                                            <a href="about.html" aria-current="page">About Us</a>
                                        </li>
                                        <li id="menu-item-18245"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18245">
                                            <a href="service.html">Services</a>
                                        </li>
                                        <li id="menu-item-15714"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-15714">
                                            <a href="portfolio.html">Portfolio</a>
                                        </li>
                                        <li id="menu-item-18244"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-18244">
                                            <a href="contact.html">Contact</a>
                                        </li>
                                    </ul>
                                </div> <a class="search-toggle toggle-link"></a>

                                <div class="search-container">
                                    <div class="search-box">
                                        <form class="search-form" method="get" action="index.html">
                                            <label>
                                                <span>
                                                    Search for </span>
                                                <input type="search" id="search-field" name="s"
                                                    placeholder="type and hit enter">
                                            </label>
                                            <input type="submit" class="search-submit" value="Search">
                                        </form> <!-- .search-form -->
                                    </div> <!-- .search-box -->
                                </div> <!-- .search-container -->
                                <div class="social-container widget-area">
                                    <a class="social-link instagram" target="_blank" href="#"></a>
                                    <a class="social-link twitter" target="_blank" href="#"></a>
                                </div> <!-- .social-container -->
                            </div> <!-- .layout-medium -->
                        </div> <!-- .menu-wrap -->
                    </nav> <!-- #site-navigation .main-navigation .site-navigation -->
                </div> <!-- .header-wrap-inner -->
            </div> <!-- .header-wrap -->
        </header> <!-- #masthead .site-header -->
        <div data-elementor-type="wp-page" data-elementor-id="17782" class="elementor elementor-17782">
            <div class="elementor-element elementor-element-6296a597 e-flex e-con-boxed e-con e-parent"
                data-id="6296a597" data-element_type="container"
                data-settings="{&quot;background_background&quot;:&quot;slideshow&quot;,&quot;background_slideshow_gallery&quot;:[{&quot;id&quot;:15800,&quot;url&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/04\/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa.jpg&quot;},{&quot;id&quot;:15792,&quot;url&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/04\/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor.jpg&quot;},{&quot;id&quot;:15793,&quot;url&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/04\/stylish-composition-cozy-living-room-interior-with-copy-space-lot-plants-wooden-shelves-rattan-sofa-accessories-beige-wall-carpet-floor-plants-love-concept-template.jpg&quot;}],&quot;background_slideshow_lazyload&quot;:&quot;yes&quot;,&quot;background_slideshow_ken_burns&quot;:&quot;yes&quot;,&quot;background_slideshow_loop&quot;:&quot;yes&quot;,&quot;background_slideshow_slide_duration&quot;:5000,&quot;background_slideshow_slide_transition&quot;:&quot;fade&quot;,&quot;background_slideshow_transition_duration&quot;:500,&quot;background_slideshow_ken_burns_zoom_direction&quot;:&quot;in&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-1ae3fc11 e-flex e-con-boxed e-con e-child"
                        data-id="1ae3fc11" data-element_type="container">
                        <div class="e-con-inner">
                            <div class="elementor-element elementor-element-7e976316 e-con-full e-flex e-con e-child"
                                data-id="7e976316" data-element_type="container">
                                <div class="elementor-element elementor-element-3286ceea elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                    data-id="3286ceea" data-element_type="widget"
                                    data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                    data-widget_type="qi_addons_for_elementor_animated_text.default">
                                    <div class="elementor-widget-container">
                                        <div
                                            class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef--alignment-center qodef-qi--has-appear qodef--appear-fade">
                                            <h1 class="qodef-m-title">
                                                <span class="qodef-e-word-holder">About</span> <span
                                                    class="qodef-e-word-holder">Us</span>
                                            </h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-262d11de e-flex e-con-boxed e-con e-parent"
                data-id="262d11de" data-element_type="container"
                data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-78f02591 e-con-full e-flex e-con e-child"
                        data-id="78f02591" data-element_type="container">
                        <div class="elementor-element elementor-element-4a2d1c2f elementor-drop-cap-yes elementor-drop-cap-view-default elementor-widget elementor-widget-text-editor"
                            data-id="4a2d1c2f" data-element_type="widget"
                            data-settings="{&quot;drop_cap&quot;:&quot;yes&quot;}"
                            data-widget_type="text-editor.default">
                            <div class="elementor-widget-container">
                                <p data-start="157" data-end="418">At Motiff Square, we believe that your
                                    space should tell your story. Our team of creative and skilled designers is
                                    committed to transforming your vision into a reality, creating environments that
                                    reflect your personality, style, and functional needs.</p>
                                <p data-start="420" data-end="678">With years of experience and a passion for innovative
                                    design, we specialize in both residential and commercial interiors. We work closely
                                    with each client to understand their preferences and goals, ensuring every project
                                    is a true reflection of who they are.</p>
                                <p data-start="680" data-end="917">From conceptualization to execution, we manage every
                                    aspect of the design process with attention to detail, craftsmanship, and a keen eye
                                    for aesthetics. Our mission is simple: to create spaces that inspire and enrich your
                                    everyday life.</p>
                                <p data-start="153" data-end="507">At Motiff Square, our mission is to craft inspiring,
                                    functional, and personalized spaces that elevate the way our clients live, work, and
                                    experience their surroundings. We are committed to delivering exceptional design
                                    solutions that blend creativity with practicality, transforming every environment
                                    into a reflection of the client’s unique vision and style.</p>
                                <p data-start="509" data-end="778">We strive to build lasting relationships with our
                                    clients, ensuring each project is executed with attention to detail, innovation, and
                                    impeccable craftsmanship. Our goal is to create spaces that inspire, enrich, and
                                    enhance the everyday lives of those who inhabit them.</p>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-37922c31 e-con-full e-flex e-con e-child"
                        data-id="37922c31" data-element_type="container"
                        data-settings="{&quot;background_background&quot;:&quot;slideshow&quot;,&quot;background_slideshow_gallery&quot;:[{&quot;id&quot;:15814,&quot;url&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/04\/3d-rendering-loft-style-living-room-interior-design.jpg&quot;},{&quot;id&quot;:15813,&quot;url&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/04\/bright-living-room-decorating-with-rattan-furniture.jpg&quot;},{&quot;id&quot;:15812,&quot;url&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/04\/cabinet-tv-white-wall-living-room-with-armchair-minimal-design-3d-rendering.jpg&quot;},{&quot;id&quot;:15811,&quot;url&quot;:&quot;https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/2025\/04\/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room.jpg&quot;}],&quot;background_slideshow_loop&quot;:&quot;yes&quot;,&quot;background_slideshow_slide_duration&quot;:5000,&quot;background_slideshow_slide_transition&quot;:&quot;fade&quot;,&quot;background_slideshow_transition_duration&quot;:500}">
                    </div>
                </div>
            </div>
            <section
                class="elementor-section elementor-top-section elementor-element elementor-element-2d9c227c elementor-section-boxed elementor-section-height-default elementor-section-height-default"
                data-id="2d9c227c" data-element_type="section">
                <div class="elementor-container elementor-column-gap-default">
                    <div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-238d2828"
                        data-id="238d2828" data-element_type="column">
                        <div class="elementor-widget-wrap elementor-element-populated">
                            <div class="elementor-element elementor-element-6490623f elementor-widget__width-initial elementor-widget elementor-widget-qi_addons_for_elementor_timeline"
                                data-id="6490623f" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_timeline.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-timeline qodef-timeline--vertical qodef-timeline-layout--vertical-separated qodef-line--outside qodef-point--standard qodef-qi--has-appear qodef-reverse-padding">
                                        <div class="qodef-e qodef-e-item elementor-repeater-item-1b6da1d qodef-obverse">
                                            <div class="qodef-e-line-holder">
                                                <span class="qodef-e-line"></span>
                                            </div>
                                            <div class="qodef-e-item-inner">
                                                <div class="qodef-e-point-holder">
                                                    <div class="qodef-e-point">
                                                    </div>
                                                </div>
                                                <div class="qodef-e-side-holder">
                                                    <div class="qodef-e-image">
                                                        <img fetchpriority="high" decoding="async" width="1300"
                                                            height="650"
                                                            src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1300x650.jpeg"
                                                            class="attachment-qi_addons_for_elementor_image_size_landscape size-qi_addons_for_elementor_image_size_landscape"
                                                            alt=""
                                                            srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1300x650.jpeg 1300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-300x150.jpeg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1024x512.jpeg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-768x384.jpeg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1100x550.jpeg 1100w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1060x530.jpeg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1920x960.jpeg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1536x768.jpeg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-2048x1024.jpeg 2048w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-550x275.jpeg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Modern-Interior-with-Plush-White-Chair-1000x500.jpeg 1000w"
                                                            sizes="(max-width: 1300px) 100vw, 1300px" />
                                                    </div>
                                                </div>
                                                <div class="qodef-e-content-holder">
                                                    <div class="qodef-e-date">
                                                        2005 </div>
                                                    <h3 class="qodef-e-title">
                                                        Company Founded </h3>
                                                    <p class="qodef-e-text">
                                                        Launched with passion to redefine spaces through innovative,
                                                        personalized interior design solutions. </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-e-item elementor-repeater-item-3497903 qodef-reverse">
                                            <div class="qodef-e-line-holder">
                                                <span class="qodef-e-line"></span>
                                            </div>
                                            <div class="qodef-e-item-inner">
                                                <div class="qodef-e-point-holder">
                                                    <div class="qodef-e-point">
                                                    </div>
                                                </div>
                                                <div class="qodef-e-side-holder">
                                                    <div class="qodef-e-image">
                                                        <img decoding="async" width="1300" height="650"
                                                            src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1300x650.jpeg"
                                                            class="attachment-qi_addons_for_elementor_image_size_landscape size-qi_addons_for_elementor_image_size_landscape"
                                                            alt=""
                                                            srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1300x650.jpeg 1300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-300x150.jpeg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1024x512.jpeg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-768x384.jpeg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1100x550.jpeg 1100w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1060x530.jpeg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1920x960.jpeg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1536x768.jpeg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-2048x1024.jpeg 2048w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-550x275.jpeg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-Scene-1000x500.jpeg 1000w"
                                                            sizes="(max-width: 1300px) 100vw, 1300px" />
                                                    </div>
                                                </div>
                                                <div class="qodef-e-content-holder">
                                                    <div class="qodef-e-date">
                                                        2008 </div>
                                                    <h3 class="qodef-e-title">
                                                        First Major Residential Project </h3>
                                                    <p class="qodef-e-text">
                                                        Completed luxury villa project, showcasing elegant, functional
                                                        design with client-centered approach. </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-e-item elementor-repeater-item-b8c0732 qodef-obverse">
                                            <div class="qodef-e-line-holder">
                                                <span class="qodef-e-line"></span>
                                            </div>
                                            <div class="qodef-e-item-inner">
                                                <div class="qodef-e-point-holder">
                                                    <div class="qodef-e-point">
                                                    </div>
                                                </div>
                                                <div class="qodef-e-side-holder">
                                                    <div class="qodef-e-image">
                                                        <img decoding="async" width="1300" height="650"
                                                            src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-1300x650.jpg"
                                                            class="attachment-qi_addons_for_elementor_image_size_landscape size-qi_addons_for_elementor_image_size_landscape"
                                                            alt="" />
                                                    </div>
                                                </div>
                                                <div class="qodef-e-content-holder">
                                                    <div class="qodef-e-date">
                                                        2014 </div>
                                                    <h3 class="qodef-e-title">
                                                        Expanded to Commercial Interiors </h3>
                                                    <p class="qodef-e-text">
                                                        Ventured into office design, blending productivity with
                                                        aesthetics in corporate workspaces. </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-e-item elementor-repeater-item-a99a7ce qodef-reverse">
                                            <div class="qodef-e-line-holder">
                                                <span class="qodef-e-line"></span>
                                            </div>
                                            <div class="qodef-e-item-inner">
                                                <div class="qodef-e-point-holder">
                                                    <div class="qodef-e-point">
                                                    </div>
                                                </div>
                                                <div class="qodef-e-side-holder">
                                                    <div class="qodef-e-image">
                                                        <img loading="lazy" decoding="async" width="1300" height="650"
                                                            src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-cozy-living-room-interior-with-copy-space-lot-plants-cacti-wooden-cubes-sofa-accessories-beige-wall-carpet-floor-plants-love-concept-template-1300x650.jpg"
                                                            class="attachment-qi_addons_for_elementor_image_size_landscape size-qi_addons_for_elementor_image_size_landscape"
                                                            alt="" />
                                                    </div>
                                                </div>
                                                <div class="qodef-e-content-holder">
                                                    <div class="qodef-e-date">
                                                        2017 </div>
                                                    <h3 class="qodef-e-title">
                                                        Opened Flagship Studio </h3>
                                                    <p class="qodef-e-text">
                                                        Established a design studio featuring interactive client
                                                        experience and concept visualization tools. </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-e-item elementor-repeater-item-678f3e2 qodef-obverse">
                                            <div class="qodef-e-line-holder">
                                                <span class="qodef-e-line"></span>
                                            </div>
                                            <div class="qodef-e-item-inner">
                                                <div class="qodef-e-point-holder">
                                                    <div class="qodef-e-point">
                                                    </div>
                                                </div>
                                                <div class="qodef-e-side-holder">
                                                    <div class="qodef-e-image">
                                                        <img loading="lazy" decoding="async" width="1300" height="650"
                                                            src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1300x650.jpg"
                                                            class="attachment-qi_addons_for_elementor_image_size_landscape size-qi_addons_for_elementor_image_size_landscape"
                                                            alt="" />
                                                    </div>
                                                </div>
                                                <div class="qodef-e-content-holder">
                                                    <div class="qodef-e-date">
                                                        2025 </div>
                                                    <h3 class="qodef-e-title">
                                                        Won Regional Design Award </h3>
                                                    <p class="qodef-e-text">
                                                        Recognized for outstanding sustainable design in boutique hotel
                                                        renovation project. </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <div class="elementor-element elementor-element-0ed7b07 e-con-full e-flex e-con e-parent" data-id="0ed7b07"
                data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="elementor-element elementor-element-5433598 e-flex e-con-boxed e-con e-child"
                    data-id="5433598" data-element_type="container">
                    <div class="e-con-inner">
                        <div class="elementor-element elementor-element-dd38090 e-con-full e-flex e-con e-child"
                            data-id="dd38090" data-element_type="container">
                            <div class="elementor-element elementor-element-8e5b880 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                data-id="8e5b880" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="qi_addons_for_elementor_animated_text.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                        <h1 class="qodef-m-title">
                                            <span class="qodef-e-word-holder">Tailored</span> <span
                                                class="qodef-e-word-holder">Interior</span> <span
                                                class="qodef-e-word-holder">Solutions</span>
                                        </h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-221e6ce e-con-full e-flex e-con e-child"
                            data-id="221e6ce" data-element_type="container">
                            <div class="elementor-element elementor-element-e2ad169 elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                data-id="e2ad169" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_animated_text.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                        <h3 class="qodef-m-title">
                                            <span class="qodef-e-word-holder">About</span> <span
                                                class="qodef-e-word-holder">Us</span>
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-72c35f6 animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                                data-id="72c35f6" data-element_type="widget"
                                data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <p class="elementor-heading-title elementor-size-default">Discover our story and
                                        commitment to transforming spaces with creativity, elegance, and thoughtful
                                        design solutions.</p>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-4fc3ffb elementor-widget elementor-widget-qi_addons_for_elementor_button"
                                data-id="4fc3ffb" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_button.default">
                                <div class="elementor-widget-container">
                                    <a class="qodef-shortcode qodef-m qodef-qi-button qodef-html--link qodef-layout--textual qodef-icon--right qodef-hover--icon-move-horizontal-short"
                                        href="about.html" target="_self">
                                        <span class="qodef-m-text">Company Info</span>
                                        <span class="qodef-m-icon">
                                            <span class="qodef-m-icon-inner">
                                                <svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 0 21 21"
                                                    width="21">
                                                    <g fill="none" fill-rule="evenodd" stroke="currentColor"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        transform="translate(6 6)">
                                                        <path d="m8.5 7.5v-7h-7"></path>
                                                        <path d="m8.5.5-8 8"></path>
                                                    </g>
                                                </svg> </span>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-a2f89fe e-flex e-con-boxed e-con e-parent" data-id="a2f89fe"
                data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-5e0e3ad e-con-full e-flex e-con e-child"
                        data-id="5e0e3ad" data-element_type="container">
                        <div class="elementor-element elementor-element-1f47a4fc elementor-widget elementor-widget-qi_addons_for_elementor_parallax_images"
                            data-id="1f47a4fc" data-element_type="widget"
                            data-widget_type="qi_addons_for_elementor_parallax_images.default">
                            <div class="elementor-widget-container">
                                <div class="qodef-shortcode qodef-m qodef-qi-parallax-images qodef-layout--default">
                                    <div class="qodef-m-images">
                                        <div class="qodef-e-main-image-holder">
                                            <div class="qodef-e-main-image-zoom-holder">
                                                <div class="qodef-e-main-image" data-parallax-main="60">
                                                    <img loading="lazy" decoding="async" width="650" height="650"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-with-Modern-Lounge-Chair-650x650.jpeg"
                                                        class="attachment-qi_addons_for_elementor_image_size_square size-qi_addons_for_elementor_image_size_square"
                                                        alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-with-Modern-Lounge-Chair-650x650.jpeg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-with-Modern-Lounge-Chair-150x150.jpeg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-with-Modern-Lounge-Chair-550x550.jpeg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/Minimalist-Interior-with-Modern-Lounge-Chair-300x300.jpeg 300w"
                                                        sizes="(max-width: 650px) 100vw, 650px" />
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="qodef-e-parallax-image qodef-position--bottom-left elementor-repeater-item-2f947ea">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-4c5481fe e-con-full e-flex e-con e-child"
                        data-id="4c5481fe" data-element_type="container">
                        <div class="elementor-element elementor-element-7ee9909c elementor-drop-cap-yes elementor-drop-cap-view-default elementor-widget elementor-widget-text-editor"
                            data-id="7ee9909c" data-element_type="widget"
                            data-settings="{&quot;drop_cap&quot;:&quot;yes&quot;}"
                            data-widget_type="text-editor.default">
                            <div class="elementor-widget-container">
                                <p data-start="151" data-end="450">At Motiff Square, our vision is to become a
                                    leader in
                                    transforming spaces through innovative and thoughtful design. We aim to create
                                    environments that not only meet the aesthetic and functional needs of our clients
                                    but also inspire creativity, foster well-being, and enhance the overall quality of
                                    life.</p>
                                <p data-start="452" data-end="804">We envision a world where every space—whether
                                    residential, commercial, or public—reflects the individuality and aspirations of
                                    those who occupy it, combining timeless design with modern elegance. Through
                                    passion, collaboration, and excellence, we aspire to shape interiors that leave a
                                    lasting impression and positively impact the communities we serve.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-569c05e1 e-flex e-con-boxed e-con e-parent"
                data-id="569c05e1" data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-252248b3 e-con-full e-flex e-con e-child"
                        data-id="252248b3" data-element_type="container">
                        <div class="elementor-element elementor-element-58b73a1e elementor-widget elementor-widget-text-editor"
                            data-id="58b73a1e" data-element_type="widget" data-widget_type="text-editor.default">
                            <div class="elementor-widget-container">
                                <p data-start="153" data-end="507">At Motiff Square, our mission is to craft inspiring,
                                    functional, and personalized spaces that elevate the way our clients live, work, and
                                    experience their surroundings. We are committed to delivering exceptional design
                                    solutions that blend creativity with practicality, transforming every environment
                                    into a reflection of the client’s unique vision and style.</p>
                                <p data-start="509" data-end="778">We strive to build lasting relationships with our
                                    clients, ensuring each project is executed with attention to detail, innovation, and
                                    impeccable craftsmanship. Our goal is to create spaces that inspire, enrich, and
                                    enhance the everyday lives of those who inhabit them.</p>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-4f5b1c86 e-con-full e-flex e-con e-child"
                        data-id="4f5b1c86" data-element_type="container">
                        <div class="elementor-element elementor-element-6fb7a47b elementor-widget elementor-widget-qi_addons_for_elementor_parallax_images"
                            data-id="6fb7a47b" data-element_type="widget"
                            data-widget_type="qi_addons_for_elementor_parallax_images.default">
                            <div class="elementor-widget-container">
                                <div class="qodef-shortcode qodef-m qodef-qi-parallax-images qodef-layout--default">
                                    <div class="qodef-m-images">
                                        <div class="qodef-e-main-image-holder">
                                            <div class="qodef-e-main-image-zoom-holder">
                                                <div class="qodef-e-main-image" data-parallax-main="60">
                                                    <img loading="lazy" decoding="async" width="650" height="650"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/home-interior-mockup-with-cozy-sofa-white-wall-background-3d-render-650x650.jpg"
                                                        class="attachment-qi_addons_for_elementor_image_size_square size-qi_addons_for_elementor_image_size_square"
                                                        alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/home-interior-mockup-with-cozy-sofa-white-wall-background-3d-render-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/home-interior-mockup-with-cozy-sofa-white-wall-background-3d-render-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/home-interior-mockup-with-cozy-sofa-white-wall-background-3d-render-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/home-interior-mockup-with-cozy-sofa-white-wall-background-3d-render-300x300.jpg 300w"
                                                        sizes="(max-width: 650px) 100vw, 650px" />
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="qodef-e-parallax-image qodef-position--bottom-left elementor-repeater-item-2f947ea">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-900d940 e-flex e-con-boxed e-con e-parent" data-id="900d940"
                data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-157e40f7 e-con-full e-flex e-con e-child"
                        data-id="157e40f7" data-element_type="container">
                        <div class="elementor-element elementor-element-36df1e5d elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                            data-id="36df1e5d" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                            data-widget_type="qi_addons_for_elementor_animated_text.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-from-right">
                                    <h1 class="qodef-m-title">
                                        <span class="qodef-e-word-holder">Showcase</span>
                                    </h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-1f8ca490 e-flex e-con-boxed e-con e-parent"
                data-id="1f8ca490" data-element_type="container"
                data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-5cb001ea e-con-full e-flex e-con e-child"
                        data-id="5cb001ea" data-element_type="container">
                        <div class="elementor-element elementor-element-733bee8c elementor-widget elementor-widget-qi_addons_for_elementor_image_gallery_masonry"
                            data-id="733bee8c" data-element_type="widget"
                            data-widget_type="qi_addons_for_elementor_image_gallery_masonry.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-image-gallery-masonry qodef-qi-fslightbox-popup qodef-popup-gallery qodef-image--hover-zoom qodef-image--hover-from-top qodef-qi-grid qodef-layout--qi-masonry qodef-items--fixed qodef-col-num--3 qodef-responsive--custom qodef-col-num--1680--3 qodef-col-num--1440--3 qodef-col-num--1366--3 qodef-col-num--1024--3 qodef-col-num--768--3 qodef-col-num--680--2 qodef-col-num--480--2">
                                    <div class="qodef-grid-inner">
                                        <div class="qodef-qi-grid-masonry-sizer"></div>
                                        <div class="qodef-e qodef-image-wrapper qodef-grid-item qodef-item--">
                                            <div class="qodef-e-inner">
                                                <a class="qodef-popup-item" itemprop="image"
                                                    href="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design.jpg"
                                                    data-type="image" data-fslightbox="gallery-1">
                                                    <img loading="lazy" decoding="async" width="1920" height="1142"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design.jpg"
                                                        class="attachment- size-" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design.jpg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-300x178.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-1024x609.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-768x457.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-925x550.jpg 925w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-1060x630.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-1536x914.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-550x327.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-841x500.jpg 841w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/3d-rendering-loft-style-living-room-interior-design-1816x1080.jpg 1816w"
                                                        sizes="(max-width: 1920px) 100vw, 1920px" /> </a>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-image-wrapper qodef-grid-item qodef-item--">
                                            <div class="qodef-e-inner">
                                                <a class="qodef-popup-item" itemprop="image"
                                                    href="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room.jpg"
                                                    data-type="image" data-fslightbox="gallery-1">
                                                    <img loading="lazy" decoding="async" width="1920" height="1080"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room.jpg"
                                                        class="attachment- size-" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room.jpg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room-300x169.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room-1024x576.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room-768x432.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room-978x550.jpg 978w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room-1060x596.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room-1536x864.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room-550x309.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/concrete-wall-mock-up-warm-tones-with-leather-sofa-which-is-kitchen-room-889x500.jpg 889w"
                                                        sizes="(max-width: 1920px) 100vw, 1920px" /> </a>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-image-wrapper qodef-grid-item qodef-item--">
                                            <div class="qodef-e-inner">
                                                <a class="qodef-popup-item" itemprop="image"
                                                    href="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa.jpg"
                                                    data-type="image" data-fslightbox="gallery-1">
                                                    <img loading="lazy" decoding="async" width="1920" height="1280"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa.jpg"
                                                        class="attachment- size-" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa.jpg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-300x200.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-1024x683.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-768x512.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-825x550.jpg 825w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-1060x707.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-1536x1024.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-550x367.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-750x500.jpg 750w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-compositon-modern-living-room-interior-with-frotte-armchair-sofa-plants-painting-wooden-commode-side-table-elegant-home-accessories-template-copy-spacexa-1620x1080.jpg 1620w"
                                                        sizes="(max-width: 1920px) 100vw, 1920px" /> </a>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-image-wrapper qodef-grid-item qodef-item--">
                                            <div class="qodef-e-inner">
                                                <a class="qodef-popup-item" itemprop="image"
                                                    href="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa.jpg"
                                                    data-type="image" data-fslightbox="gallery-1">
                                                    <img loading="lazy" decoding="async" width="1920" height="1920"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa.jpg"
                                                        class="attachment- size-" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa.jpg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1024x1024.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-768x768.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1060x1060.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1536x1536.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-500x500.jpg 500w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1080x1080.jpg 1080w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/minimalist-composition-modern-living-room-interior-details-creative-vases-wooden-side-table-home-staging-template-copy-space-xa-1300x1300.jpg 1300w"
                                                        sizes="(max-width: 1920px) 100vw, 1920px" /> </a>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-image-wrapper qodef-grid-item qodef-item--">
                                            <div class="qodef-e-inner">
                                                <a class="qodef-popup-item" itemprop="image"
                                                    href="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor.jpg"
                                                    data-type="image" data-fslightbox="gallery-1">
                                                    <img loading="lazy" decoding="async" width="1920" height="1920"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor.jpg"
                                                        class="attachment- size-" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor.jpg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-300x300.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-1024x1024.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-150x150.jpg 150w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-768x768.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-550x550.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-1060x1060.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-1536x1536.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-500x500.jpg 500w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-1080x1080.jpg 1080w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-650x650.jpg 650w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-composition-creative-cozy-living-room-interior-with-grey-sofa-coffee-table-plants-carpet-beautiful-accessories-white-walls-parquet-floor-1300x1300.jpg 1300w"
                                                        sizes="(max-width: 1920px) 100vw, 1920px" /> </a>
                                            </div>
                                        </div>
                                        <div class="qodef-e qodef-image-wrapper qodef-grid-item qodef-item--">
                                            <div class="qodef-e-inner">
                                                <a class="qodef-popup-item" itemprop="image"
                                                    href="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template.jpg"
                                                    data-type="image" data-fslightbox="gallery-1">
                                                    <img loading="lazy" decoding="async" width="1920" height="1280"
                                                        src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template.jpg"
                                                        class="attachment- size-" alt=""
                                                        srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template.jpg 1920w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-300x200.jpg 300w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1024x683.jpg 1024w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-768x512.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-825x550.jpg 825w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1060x707.jpg 1060w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1536x1024.jpg 1536w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-550x367.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-750x500.jpg 750w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/stylish-dining-room-with-round-table-rattan-chair-wooden-commode-poster-kitchen-accessories-beige-wall-with-mock-up-poster-home-decor-template-1620x1080.jpg 1620w"
                                                        sizes="(max-width: 1920px) 100vw, 1920px" /> </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-a949d7f e-flex e-con-boxed e-con e-parent" data-id="a949d7f"
                data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-60aec55 e-con-full e-flex e-con e-child"
                        data-id="60aec55" data-element_type="container">
                        <div class="elementor-element elementor-element-6b223d4 elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                            data-id="6b223d4" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                            data-widget_type="qi_addons_for_elementor_animated_text.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                    <h1 class="qodef-m-title">
                                        <span class="qodef-e-word-holder">Meet</span> <span
                                            class="qodef-e-word-holder">Our</span> <span
                                            class="qodef-e-word-holder">Team</span>
                                    </h1>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-1513763 e-con-full e-flex e-con e-child"
                        data-id="1513763" data-element_type="container">
                        <div class="elementor-element elementor-element-fa48c30 elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                            data-id="fa48c30" data-element_type="widget"
                            data-widget_type="qi_addons_for_elementor_animated_text.default">
                            <div class="elementor-widget-container">
                                <div
                                    class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef-qi--has-appear qodef--appear-fade">
                                    <h3 class="qodef-m-title">
                                        <span class="qodef-e-word-holder">Team</span>
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-12344ff animated-slow elementor-invisible elementor-widget elementor-widget-heading"
                            data-id="12344ff" data-element_type="widget"
                            data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                            data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <p class="elementor-heading-title elementor-size-default">Unleashing potential through
                                    teamwork and innovation. Driving excellence, one project at a time, for a brighter
                                    future.</p>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-70f3f93 e-con-full e-flex e-con e-child"
                        data-id="70f3f93" data-element_type="container">
                        <div class="elementor-element elementor-element-fedf593 e-con-full e-flex e-con e-child"
                            data-id="fedf593" data-element_type="container">
                            <div class="elementor-element elementor-element-2a435b4 elementor-widget elementor-widget-qi_addons_for_elementor_banner"
                                data-id="2a435b4" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_banner.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-banner qodef-layout--standard qodef-vertical--bottom qodef-horizontal--left qodef-image--hover-zoom">
                                        <div class="qodef-m-image">
                                            <img loading="lazy" decoding="async" width="800" height="900"
                                                src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02.jpg"
                                                class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
                                                alt=""
                                                srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-02-444x500.jpg 444w"
                                                sizes="(max-width: 800px) 100vw, 800px" />
                                        </div>
                                        <div class="qodef-m-content">
                                            <div class="qodef-m-content-inner">
                                                <h5 class="qodef-m-subtitle">
                                                    Furniture Designer </h5>
                                                <h3 class="qodef-m-title">
                                                    Oliver James </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-81682ca e-con-full e-flex e-con e-child"
                            data-id="81682ca" data-element_type="container">
                            <div class="elementor-element elementor-element-eebaca8 elementor-widget elementor-widget-qi_addons_for_elementor_banner"
                                data-id="eebaca8" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_banner.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-banner qodef-layout--standard qodef-vertical--bottom qodef-horizontal--left qodef-image--hover-zoom">
                                        <div class="qodef-m-image">
                                            <img loading="lazy" decoding="async" width="800" height="900"
                                                src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21.jpg"
                                                class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
                                                alt=""
                                                srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-21-444x500.jpg 444w"
                                                sizes="(max-width: 800px) 100vw, 800px" />
                                        </div>
                                        <div class="qodef-m-content">
                                            <div class="qodef-m-content-inner">
                                                <h5 class="qodef-m-subtitle">
                                                    Creative Director </h5>
                                                <h3 class="qodef-m-title">
                                                    Anna Hallberg </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-5efad86 e-con-full e-flex e-con e-child"
                            data-id="5efad86" data-element_type="container">
                            <div class="elementor-element elementor-element-22d3072 elementor-widget elementor-widget-qi_addons_for_elementor_banner"
                                data-id="22d3072" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_banner.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-banner qodef-layout--standard qodef-vertical--bottom qodef-horizontal--left qodef-image--hover-zoom">
                                        <div class="qodef-m-image">
                                            <img loading="lazy" decoding="async" width="800" height="900"
                                                src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14.jpg"
                                                class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
                                                alt=""
                                                srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-14-444x500.jpg 444w"
                                                sizes="(max-width: 800px) 100vw, 800px" />
                                        </div>
                                        <div class="qodef-m-content">
                                            <div class="qodef-m-content-inner">
                                                <h5 class="qodef-m-subtitle">
                                                    Senior Stylist </h5>
                                                <h3 class="qodef-m-title">
                                                    Henry Patel </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-f077931 e-con-full e-flex e-con e-child"
                            data-id="f077931" data-element_type="container">
                            <div class="elementor-element elementor-element-e9d5a47 elementor-widget elementor-widget-qi_addons_for_elementor_banner"
                                data-id="e9d5a47" data-element_type="widget"
                                data-widget_type="qi_addons_for_elementor_banner.default">
                                <div class="elementor-widget-container">
                                    <div
                                        class="qodef-shortcode qodef-m qodef-qi-banner qodef-layout--standard qodef-vertical--bottom qodef-horizontal--left qodef-image--hover-zoom">
                                        <div class="qodef-m-image">
                                            <img loading="lazy" decoding="async" width="800" height="900"
                                                src="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08.jpg"
                                                class="attachment-qi_addons_for_elementor_image_size_huge-square size-qi_addons_for_elementor_image_size_huge-square"
                                                alt=""
                                                srcset="https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08.jpg 800w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-267x300.jpg 267w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-768x864.jpg 768w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-489x550.jpg 489w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-550x619.jpg 550w, https://themes.pixelwars.org/interique/demo-01/wp-content/uploads/sites/2/2025/04/t-08-444x500.jpg 444w"
                                                sizes="(max-width: 800px) 100vw, 800px" />
                                        </div>
                                        <div class="qodef-m-content">
                                            <div class="qodef-m-content-inner">
                                                <h5 class="qodef-m-subtitle">
                                                    Creative Director </h5>
                                                <h3 class="qodef-m-title">
                                                    Sophia Bennett </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-cd2a6a9 e-flex e-con-boxed e-con e-parent" data-id="cd2a6a9"
                data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-4cf692c e-con-full e-flex e-con e-child"
                        data-id="4cf692c" data-element_type="container">
                        <div class="elementor-element elementor-element-362bef7 e-con-full e-flex e-con e-child"
                            data-id="362bef7" data-element_type="container">
                            <div class="elementor-element elementor-element-c582837 e-con-full e-flex e-con e-child"
                                data-id="c582837" data-element_type="container">
                                <div class="elementor-element elementor-element-26b45fa elementor-widget__width-initial elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                    data-id="26b45fa" data-element_type="widget"
                                    data-widget_type="qi_addons_for_elementor_animated_text.default">
                                    <div class="elementor-widget-container">
                                        <div
                                            class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef--alignment-center qodef-qi--has-appear qodef--appear-fade">
                                            <h3 class="qodef-m-title">
                                                <span class="qodef-e-word-holder">—</span> <span
                                                    class="qodef-e-word-holder">Collaboration</span>
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-81a57ec elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_animated_text"
                                    data-id="81a57ec" data-element_type="widget"
                                    data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}"
                                    data-widget_type="qi_addons_for_elementor_animated_text.default">
                                    <div class="elementor-widget-container">
                                        <div
                                            class="qodef-shortcode qodef-m qodef-qi-animated-text qodef--animated-by-letter qodef--alignment-center qodef-qi--has-appear qodef--appear-fade">
                                            <h1 class="qodef-m-title">
                                                <span class="qodef-e-word-holder">Let’s</span> <span
                                                    class="qodef-e-word-holder">Create</span> <span
                                                    class="qodef-e-word-holder">Something</span> <span
                                                    class="qodef-e-word-holder">Beautiful</span> <span
                                                    class="qodef-e-word-holder">Together</span>
                                            </h1>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-363c752 elementor-widget elementor-widget-spacer"
                                    data-id="363c752" data-element_type="widget" data-widget_type="spacer.default">
                                    <div class="elementor-widget-container">
                                        <div class="elementor-spacer">
                                            <div class="elementor-spacer-inner"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-0ea2437 -text-faded elementor-invisible elementor-widget elementor-widget-heading"
                                    data-id="0ea2437" data-element_type="widget"
                                    data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:400}"
                                    data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h3 class="elementor-heading-title elementor-size-default">Ready to transform
                                            your space? We’d love to hear from you! <u>Get in touch</u> today to
                                            schedule a consultation and start bringing your vision to life.</h3>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-cb1ab1e elementor-widget elementor-widget-spacer"
                                    data-id="cb1ab1e" data-element_type="widget" data-widget_type="spacer.default">
                                    <div class="elementor-widget-container">
                                        <div class="elementor-spacer">
                                            <div class="elementor-spacer-inner"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-cfdad01 e-transform elementor-invisible elementor-widget elementor-widget-qi_addons_for_elementor_button"
                                    data-id="cfdad01" data-element_type="widget"
                                    data-settings="{&quot;_animation&quot;:&quot;fadeInUp&quot;,&quot;_animation_delay&quot;:800,&quot;_transform_scale_effect_hover&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:1.100000000000000088817841970012523233890533447265625,&quot;sizes&quot;:[]},&quot;_transform_scale_effect_hover_tablet&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:[]},&quot;_transform_scale_effect_hover_mobile&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:[]}}"
                                    data-widget_type="qi_addons_for_elementor_button.default">
                                    <div class="elementor-widget-container">
                                        <a class="qodef-shortcode qodef-m qodef-qi-button qodef-html--link qodef-layout--filled qodef-type--standard qodef-icon--right qodef-hover--icon-move-horizontal-short"
                                            href="contact.html"
                                            target="_self">
                                            <span class="qodef-m-text">Get In Touch</span>
                                            <span class="qodef-m-icon">
                                                <span class="qodef-m-icon-inner">
                                                    <svg xmlns="http://www.w3.org/2000/svg" height="21"
                                                        viewBox="0 0 21 21" width="21">
                                                        <g fill="none" fill-rule="evenodd" stroke="currentColor"
                                                            stroke-linecap="round" stroke-linejoin="round"
                                                            transform="translate(6 6)">
                                                            <path d="m8.5 7.5v-7h-7"></path>
                                                            <path d="m8.5.5-8 8"></path>
                                                        </g>
                                                    </svg> </span>
                                            </span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <footer id="colophon" class="site-footer" role="contentinfo">
            <div class="site-info">

                <p>© 2025 Motiff Square </p>
            </div> <!-- .site-info -->
        </footer> <!-- #colophon .site-footer -->
    </div>
    <script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/interique\/demo-01\/*"},{"not":{"href_matches":["\/interique\/demo-01\/wp-*.php","\/interique\/demo-01\/wp-admin\/*","\/interique\/demo-01\/wp-content\/uploads\/sites\/2\/*","\/interique\/demo-01\/wp-content\/*","\/interique\/demo-01\/wp-content\/plugins\/*","\/interique\/demo-01\/wp-content\/themes\/interique\/*","\/interique\/demo-01\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
    <script>
        const lazyloadRunObserver = () => {
            const lazyloadBackgrounds = document.querySelectorAll(`.e-con.e-parent:not(.e-lazyloaded)`);
            const lazyloadBackgroundObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        let lazyloadBackground = entry.target;
                        if (lazyloadBackground) {
                            lazyloadBackground.classList.add('e-lazyloaded');
                        }
                        lazyloadBackgroundObserver.unobserve(entry.target);
                    }
                });
            }, { rootMargin: '200px 0px 200px 0px' });
            lazyloadBackgrounds.forEach((lazyloadBackground) => {
                lazyloadBackgroundObserver.observe(lazyloadBackground);
            });
        };
        const events = [
            'DOMContentLoaded',
            'elementor/lazyload/observe',
        ];
        events.forEach((event) => {
            document.addEventListener(event, lazyloadRunObserver);
        });
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3"
        id="jquery-ui-core-js"></script>
    <script type="text/javascript" id="qi-addons-for-elementor-script-js-extra">
        /* <![CDATA[ */
        var qodefQiAddonsGlobal = { "vars": { "adminBarHeight": 0, "iconArrowLeft": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0.5\" y1=\"16\" x2=\"33.5\" y2=\"16\"\/><line x1=\"0.3\" y1=\"16.5\" x2=\"16.2\" y2=\"0.7\"\/><line x1=\"0\" y1=\"15.4\" x2=\"16.2\" y2=\"31.6\"\/><\/svg>", "iconArrowRight": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 34.2 32.3\" xml:space=\"preserve\" style=\"stroke-width: 2;\"><line x1=\"0\" y1=\"16\" x2=\"33\" y2=\"16\"\/><line x1=\"17.3\" y1=\"0.7\" x2=\"33.2\" y2=\"16.5\"\/><line x1=\"17.3\" y1=\"31.6\" x2=\"33.5\" y2=\"15.4\"\/><\/svg>", "iconClose": "<svg  xmlns=\"http:\/\/www.w3.org\/2000\/svg\" x=\"0px\" y=\"0px\" viewBox=\"0 0 9.1 9.1\" xml:space=\"preserve\"><g><path d=\"M8.5,0L9,0.6L5.1,4.5L9,8.5L8.5,9L4.5,5.1L0.6,9L0,8.5L4,4.5L0,0.6L0.6,0L4.5,4L8.5,0z\"\/><\/g><\/svg>" } };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/js/main.min.js?ver=1.8.9"
        id="qi-addons-for-elementor-script-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.fitvids.js"
        id="fitvids-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jarallax.min.js"
        id="jarallax-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jarallax-video.min.js"
        id="jarallax-video-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/fluidbox/jquery.fluidbox.min.js"
        id="fluidbox-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery-validation/jquery.validate.js"
        id="jqueryvalidation-js"></script>
    <script type="text/javascript" id="jqueryvalidation-js-after">
        /* <![CDATA[ */
        (function ($) {
            "use strict";
            $.extend($.validator.messages, {
                required: "This field is required.",
                remote: "Please fix this field.",
                email: "Please enter a valid email address.",
                url: "Please enter a valid URL.",
                date: "Please enter a valid date.",
                dateISO: "Please enter a valid date ( ISO ).",
                number: "Please enter a valid number.",
                digits: "Please enter only digits.",
                equalTo: "Please enter the same value again.",
                maxlength: $.validator.format("Please enter no more than {0} characters."),
                minlength: $.validator.format("Please enter at least {0} characters."),
                rangelength: $.validator.format("Please enter a value between {0} and {1} characters long."),
                range: $.validator.format("Please enter a value between {0} and {1}."),
                max: $.validator.format("Please enter a value less than or equal to {0}."),
                min: $.validator.format("Please enter a value greater than or equal to {0}."),
                step: $.validator.format("Please enter a multiple of {0}.")
            });
        })(jQuery);
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/masonry/assets/js/plugins/isotope.pkgd.min.js?ver=3.0.6"
        id="isotope-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.magnific-popup/jquery.magnific-popup.min.js"
        id="magnific-popup-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/owl-carousel/owl.carousel.min.js"
        id="owl-carousel-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/imagesloaded.min.js?ver=5.0.0"
        id="imagesloaded-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.collagePlus.min.js"
        id="collagePlus-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.fittext.js"
        id="fittext-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/resize-sensor.js"
        id="resize-sensor-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/jquery.sticky-sidebar.min.js"
        id="sticky-sidebar-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/themes/interique/js/main.js"
        id="interique-main-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/pixelwars-core/themes/global/js/shortcodes.js"
        id="pixelwars-core-shortcodes-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.29.0"
        id="elementor-webpack-runtime-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.29.0"
        id="elementor-frontend-modules-js"></script>
    <script type="text/javascript" id="elementor-frontend-js-before">
        /* <![CDATA[ */
        var elementorFrontendConfig = { "environmentMode": { "edit": false, "wpPreview": false, "isScriptDebug": false }, "i18n": { "shareOnFacebook": "Share on Facebook", "shareOnTwitter": "Share on Twitter", "pinIt": "Pin it", "download": "Download", "downloadImage": "Download image", "fullscreen": "Fullscreen", "zoom": "Zoom", "share": "Share", "playVideo": "Play Video", "previous": "Previous", "next": "Next", "close": "Close", "a11yCarouselPrevSlideMessage": "Previous slide", "a11yCarouselNextSlideMessage": "Next slide", "a11yCarouselFirstSlideMessage": "This is the first slide", "a11yCarouselLastSlideMessage": "This is the last slide", "a11yCarouselPaginationBulletMessage": "Go to slide" }, "is_rtl": false, "breakpoints": { "xs": 0, "sm": 480, "md": 768, "lg": 1025, "xl": 1440, "xxl": 1600 }, "responsive": { "breakpoints": { "mobile": { "label": "Mobile Portrait", "value": 767, "default_value": 767, "direction": "max", "is_enabled": true }, "mobile_extra": { "label": "Mobile Landscape", "value": 880, "default_value": 880, "direction": "max", "is_enabled": false }, "tablet": { "label": "Tablet Portrait", "value": 1024, "default_value": 1024, "direction": "max", "is_enabled": true }, "tablet_extra": { "label": "Tablet Landscape", "value": 1200, "default_value": 1200, "direction": "max", "is_enabled": false }, "laptop": { "label": "Laptop", "value": 1366, "default_value": 1366, "direction": "max", "is_enabled": false }, "widescreen": { "label": "Widescreen", "value": 2400, "default_value": 2400, "direction": "min", "is_enabled": false } }, "hasCustomBreakpoints": false }, "version": "3.29.0", "is_static": false, "experimentalFeatures": { "e_font_icon_svg": true, "additional_custom_breakpoints": true, "container": true, "e_local_google_fonts": true, "nested-elements": true, "e_element_cache": true, "home_screen": true, "launchpad-checklist": true, "cloud-library": true, "e_opt_in_v4_page": true }, "urls": { "assets": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/plugins\/elementor\/assets\/", "ajaxurl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-admin\/admin-ajax.php", "uploadUrl": "https:\/\/themes.pixelwars.org\/interique\/demo-01\/wp-content\/uploads\/sites\/2" }, "nonces": { "floatingButtonsClickTracking": "5ab3af599e" }, "swiperClass": "swiper", "settings": { "page": [], "editorPreferences": [] }, "kit": { "active_breakpoints": ["viewport_mobile", "viewport_tablet"], "global_image_lightbox": "yes", "lightbox_enable_counter": "yes", "lightbox_enable_fullscreen": "yes", "lightbox_enable_zoom": "yes", "lightbox_enable_share": "yes", "lightbox_title_src": "title", "lightbox_description_src": "description" }, "post": { "id": 17782, "title": "About%20Us%20%E2%80%93%20Interiqu%C3%A9", "excerpt": "", "featuredImage": false } };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.29.0"
        id="elementor-frontend-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/plugins/swiper/8.4.5/swiper.min.js?ver=8.4.5"
        id="swiper-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/shortcodes/parallax-images/assets/js/plugins/jquery.parallax-scroll.js?ver=1"
        id="parallax-scroll-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/assets/plugins/fslightbox/fslightbox.min.js?ver=6.8.1"
        id="fslightbox-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/masonry/assets/js/plugins/packery-mode.pkgd.min.js?ver=2.0.1"
        id="packery-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/bdthemes-prime-slider-lite/assets/js/prime-slider-site.min.js?ver=3.17.12"
        id="prime-slider-site-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6"
        id="wp-hooks-js"></script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6"
        id="wp-i18n-js"></script>
    <script type="text/javascript" id="wp-i18n-js-after">
        /* <![CDATA[ */
        wp.i18n.setLocaleData({ 'text direction\u0004ltr': ['ltr'] });
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://themes.pixelwars.org/interique/demo-01/wp-content/plugins/qi-addons-for-elementor/inc/plugins/elementor/assets/js/elementor.js?ver=6.8.1"
        id="qi-addons-for-elementor-elementor-js"></script>
</body>

</html>